
from pydub import AudioSegment
from concurrent.futures import ThreadPoolExecutor, as_completed
import os
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from config import config
from utils.utlis import run_command
from notebook_index import read_csv, resolve_df
from utils.file import is_file_exist, mk_dirs
from utils.logger import logger

# 获取音频长度
def get_audio_length(file_path):
    audio = AudioSegment.from_file(file_path)
    return len(audio)

# 计算缩放倍速


def calculate_scale_factor(target_duration, current_duration):
    # 如果插值不大，人无法感知
    if abs(current_duration-target_duration) < 2000:
        return 1
    if current_duration > target_duration:
        return min(current_duration/target_duration, 1.1)
    if current_duration <= target_duration:
        return max(current_duration/target_duration, 1)
    return current_duration/target_duration
# 转换音频文件


def convert_wav_file(input_file, output_file, target_sr=44100, scale_factor=1.0):
    # 构建FFmpeg命令
    command = [
        'ffmpeg',
        "-y",
        '-loglevel',
        'error',
        '-i', f'"{input_file}"',
    ]
    if not config.only_scale:
        command += [
            '-ar', str(target_sr),
            '-acodec', 'pcm_s16le',
        ]
    command += ['-filter:a', f'atempo={scale_factor}', f'"{output_file}"']
    # 执行FFmpeg命令
    run_command(" ".join(command))


# 处理每一行
def process_row(row):
    origin_wav_file = os.path.join(
        config.wav_file_dir, row[config.file_path_column])
    temp_wav_file = os.path.join(
        config.target_folder, row[config.file_path_column])

    new_target_wav_file = os.path.join(
        new_target_folder, row[config.file_path_column])
    if is_file_exist(origin_wav_file) and is_file_exist(temp_wav_file) and not is_file_exist(new_target_wav_file):
        mk_dirs(new_target_wav_file)
        scale = calculate_scale_factor(
            get_audio_length(origin_wav_file), get_audio_length(temp_wav_file))
        logger.info(f"{origin_wav_file} 缩放比例:{scale}")
        convert_wav_file(temp_wav_file, new_target_wav_file, scale_factor=scale)




# 重新采样16位+44100+缩放
def main():
    global new_target_folder
    new_target_folder = os.path.join(config.target_folder, "sample")
    df = read_csv(config.config_file)
    df = resolve_df(df)
    # 使用 ThreadPoolExecutor 进行多线程处理
    with ThreadPoolExecutor(max_workers=16) as executor:
        futures = {executor.submit(process_row, row): index for index, row in df.iterrows()}
        for future in as_completed(futures):
            index = futures[future]
            try:
                future.result()
                logger.info(f"处理进度{index} 完成")
            except Exception as e:
                logger.error(f"处理进度{index} 发生错误: {e}")
    return new_target_folder

if __name__ == "__main__":
    main()

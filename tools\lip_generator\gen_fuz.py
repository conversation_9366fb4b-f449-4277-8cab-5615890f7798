
import os
import sys
import csv
import uuid

from utils.file import is_file_exist

sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from config import config
from notebook_index import filter_row, read_csv, replace_suffix, resolve_df
from utils.utlis import chinese_to_pinyin, run_command
from utils.logger import logger

fieldnames = ["State", "Plugin", "Filename",
              "Voice Type", "Dialogue 1 - en", "Dialogue 2 - en"]


def get_data(row):
    return {"State": row["State"], "Plugin": row["Plugin"], "Filename": row["File Name"],
            "Voice Type": row["Voice Type"], "Dialogue 1 - en": row["Dialogue 1 - English"].replace('\n', ' ').replace('\r', ' '), "Dialogue 2 - en": chinese_to_pinyin(row[config.zh_column].replace('\n', ' ').replace('\r', ' '))}


def process_rows(rows):
    temp_config_file = f"{'fuz' if config.gen_fuz else 'lip'}_{uuid.uuid4()}.csv"
    # 定义CSV文件的列名
    last_row = None
    # 创建CSV文件并写入列名
    with open(temp_config_file, mode='w', newline='', encoding="utf-8") as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        # 写入表头
        writer.writeheader()
        for index, row in rows.iterrows():
            if not filter_row(row):
                continue
            origin_lip_file = os.path.join(config.wav_file_dir, replace_suffix(row[config.file_path_column], ".lip"))
            target_wav_file = os.path.join(config.target_folder, row[config.file_path_column])
            target_lip_file = os.path.join(config.target_folder, config.gen_fuz_path, replace_suffix(row[config.file_path_column], ".fuz" if config.gen_fuz else ".lip"))
            # 目标.wav存在，原来有lip，目标没有lip，则生成lip
            if is_file_exist(target_wav_file) and is_file_exist(origin_lip_file) and not is_file_exist(target_lip_file):
                writer.writerow(get_data(row))
                last_row = row
        if last_row is not None:
            writer.writerow(get_data(last_row))
    if last_row is not None:
        input_dir = os.path.join(config.target_folder, 'sound/voice')
        output_dir = os.path.join(config.target_folder, config.gen_fuz_path, 'sound/voice')
        command = f'"{config.run_lip_path}" -genlips "SSE" "{temp_config_file}" "{input_dir}" "{output_dir}"'
        if config.gen_fuz:
            command = command + ' -nfuz'
        run_command(command)
    if os.path.exists(temp_config_file):
        os.remove(temp_config_file)


def main():
    df = read_csv(config.config_file)
    df = resolve_df(df)
    # 按每 100 行分批处理
    batch_size = 100
    total_batches = (len(df) + batch_size - 1) // batch_size  # 计算总批次
    for i in range(0, len(df), batch_size):
        batch_index = i // batch_size + 1
        batch_df = df.iloc[i:i + batch_size]
        logger.info(
            f"Processing batch {batch_index}/{total_batches}, rows {i} to {i + len(batch_df) - 1}")
        process_rows(batch_df)


if __name__ == "__main__":
    main()

import os
os.environ['HF_HUB_CACHE'] = './checkpoints/hf_cache'
import gradio as gr

from notebook_index import filter_row, read_csv, resolve_df
from utils.file import is_file_exist
from config import config

# 该函数有3个输入参数和2个输出参数


def set_config(wav_file_dir, target_folder, config_file):
    config.wav_file_dir = wav_file_dir
    config.target_folder = target_folder
    config.log_json = os.path.join(target_folder, r'logs.json')
    config.tts_folder = os.path.join(target_folder, "tts")
    config.config_file = config_file
    return f"log_json:{config.log_json}\n tts_folder:{config.tts_folder}\n target_folder:{config.target_folder}\n"


with gr.Blocks(title="AI Chinese Voice WebUI") as demo:
    with gr.Tab("选择 WAV 文件夹"):
        with gr.Row():
            wav_file_dir_input = gr.Textbox(
                label="WAV 文件夹路径", value=config.wav_file_dir)
            global target_folder_input
            target_folder_input = gr.Textbox(
                label="目标文件夹路径", value=config.target_folder)
            global config_file
            config_file = gr.Textbox(
                label="配置文件", value=config.config_file)
            select_wav_file_dir_button = gr.Button("设置")
            select_wav_file_dir_output = gr.Textbox(label="设置结果")
            select_wav_file_dir_button.click(set_config, inputs=[
                                             wav_file_dir_input, target_folder_input, config_file], outputs=[select_wav_file_dir_output])
        gr.Markdown(value="处理配置文件(忽略不需要翻译的音频)")
        with gr.Row():
            def resolve_config():
                from tools.ignore_by_ai import ignore
                config.config_file = ignore.ignore()
                return f'生成成功，见目录：{config.config_file}', config.config_file
            resolve_config_button = gr.Button("处理")
            resolve_config_output = gr.Textbox(label="处理结果")
            resolve_config_button.click(resolve_config, inputs=[], outputs=[
                                        resolve_config_output, config_file])

        gr.Markdown(value="音色克隆(需要事先下载好模型才能选择)")
        with gr.Row():
            mode_type = gr.Radio(
                choices=['seed_vc', 'spark_tts', 'cosy_voice'], label="模型选择", value='seed_vc')

            def clone(type):
                config.stop = False
                if type == 'seed_vc':
                    import models.seed_vc.index
                    models.seed_vc.index.main()
                elif type == 'spark_tts':
                    import models.spark_tts.index
                    models.spark_tts.index.main()
                elif type == 'cosy_voice':
                    import models.cosy_voice.index
                    models.cosy_voice.index.main()
                if config.stop:
                    config.stop = False
                    return f'克隆停止，见目录：{config.target_folder}'
                return f'克隆完成，见目录：{config.target_folder}'

            def stop():
                config.stop = True
            clone_button = gr.Button("克隆音色")
            stop_button = gr.Button("停止")
            stop_button.click(stop)
            clone_output = gr.Textbox(label="克隆结果")
            clone_button.click(
                clone, inputs=[mode_type], outputs=[clone_output])

        # gr.Markdown(value="重新采样")
        # with gr.Row():
        #     only_scale = gr.Radio(
        #         choices=['only_scale'], label="是否只调整速度", value='only_scale')

        #     def sample_func(type):
        #         from lip_generator import sample
        #         if type == 'only_scale':
        #             config.only_scale = True
        #         else:
        #             config.only_scale = False
        #         config.target_folder = sample.main()
        #         return f'采样完成，见目录：{config.target_folder}', config.target_folder
        #     sample_button = gr.Button("开始采样")
        #     sample_output = gr.Textbox(label="采样结果")
        #     sample_button.click(sample_func, inputs=[only_scale], outputs=[
        #                         sample_output, target_folder_input])
        gr.Markdown(value="生成口型文件")
        with gr.Row():
            file_type = gr.Radio(
                choices=['fuz', 'lip'], label="文件格式", value='lip')

            def gen_fuz_files(file_type):
                from tools.lip_generator import gen_fuz
                config.gen_fuz = file_type == 'fuz'
                gen_fuz.main()
                return f'生成成功，见目录：{os.path.join(config.target_folder, config.gen_fuz_path)}'
            gen_fuz_button = gr.Button("生成")
            gen_fuz_output = gr.Textbox(label="生成结果")
            gen_fuz_button.click(gen_fuz_files, inputs=[
                                 file_type], outputs=[gen_fuz_output])
        gr.Markdown(value="合并文件(将音频和口型文件移动到同一目录下)")
        with gr.Row():
            def move():
                from utils import move_by_config
                print('开始移动')
                res = move_by_config.move()
                return f'移动成功，见目录：{config.target_folder} 子文件夹：{res}'
            move_button = gr.Button("移动")
            move_output = gr.Textbox(label="结果")
            move_button.click(move, inputs=[], outputs=[move_output])
    with gr.Tab("复制文件夹"):
        origin_dir = gr.Textbox(
            label="源目录", value=r"origin")
        target_dir = gr.Textbox(
            label="目标目录", value=r"target")
        suffix = gr.Textbox(label="后缀名", value='.wav')
        button = gr.Button("复制")
        success = gr.Textbox(label="复制结果")

        def copy_files(origin_dir, target_dir, suffix):
            import utils.file as file
            try:
                file.copy_files(origin_dir, target_dir, suffix)
                return f"复制成功"
            except Exception as e:
                return f"复制失败: {str(e)}"
        button.click(copy_files, inputs=[
                     origin_dir, target_dir, suffix], outputs=[success])
    with gr.Tab("AI 文本转语音(tts)，不做音色克隆"):
        button = gr.Button("tts")
        success = gr.Textbox(label="tts结果")

        def tts():
            import models.seed_vc.just_tts as just_tts
            try:
                just_tts.main()
                return f"tts成功"
            except Exception as e:
                return f"tts失败: {str(e)}"
        button.click(tts, inputs=[], outputs=[success])
    with gr.Tab("音频预览"):
        def preview_audio(other_dir,page,page_size):
            df = read_csv(config.config_file)
            df = resolve_df(df)
            res = []
            other_dirs = other_dir.split("\n")
            head_str = ''
            for other_dir in other_dirs:
                if other_dir == '':
                    continue
                head_str += "<td>%s</td>"%other_dir
            # df按file_path_column排序
            df = df.sort_values(by=config.file_path_column)
            for index, row in df.iterrows():
                if not filter_row(row):
                    continue
                origin_file = os.path.join(
                    config.wav_file_dir, row[config.file_path_column])
                target_file = os.path.join(
                    config.target_folder, row[config.file_path_column])
                if not is_file_exist(target_file):
                    continue
                origin_file_url = f'http://localhost:8080/file?filename={origin_file}'
                target_file_url = f'http://localhost:8080/file?filename={target_file}'
                td_str = ""
                for dir_item in other_dirs:
                    if dir_item == '':
                        continue
                    file = os.path.join(dir_item, row[config.file_path_column])
                    if is_file_exist(file):
                        td_str+="<td><audio src='%s' preload='none' controls></audio></td>"% f'http://localhost:8080/file?filename={file}'
                    else:
                        td_str+="<td></td>"
                res.append(
                    "<tr><td>%s</td><td>%s</td><td><audio src='%s' preload='none' controls></audio></td><td><audio src='%s' preload='none' controls></audio></td>%s</tr>"%(row[config.group_column],row[config.zh_column],origin_file_url,target_file_url,td_str))
                    
            return ("<style>tr{content-visibility:auto}</style><table border><thead><tr><td>分类</td><td>汉语</td><td>英语语音</td><td>汉语语音</td>%s</tr></thead><tbody>%s</tbody></table>"%(head_str, '\n'.join(res[page*page_size:(page+1)*page_size]))),len(res),len(res)//page_size + 1
        with gr.Row():
            other_dir = gr.Textbox(
                label="其他目录", value=r"",lines=5, placeholder="可以设置其他模型生成的音频目录，一行一个，用于对比生成音频效果")
        with gr.Row():   
            page = gr.Number(
                label="页码", value=0)
            page_size = gr.Number(
                label="页大小", value=1000)
            global total
            total = gr.Number(
                label="总数", value=0)
            global total_size
            total_size = gr.Number(
                label="总页数", value=0)
        preview_button = gr.Button("预览")
        preview_html = gr.HTML('音频')
        preview_button.click(preview_audio, inputs=[other_dir,page,page_size], outputs=[preview_html,total,total_size])
demo.launch()

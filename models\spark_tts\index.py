import asyncio
import os
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from models.spark_tts.tts import tts
import notebook_index
from config import config
from utils.file import mk_dirs


def prepare():
    print('prepare')


def clone_voice(origin_file, target_file, tts_text, tts_file, emotion):
    mk_dirs(target_file)
    return tts(tts_text, target_file, origin_file)

def main():
    notebook_index.prepare = prepare
    notebook_index.clone_voice = clone_voice
    config.need_tts = False
    asyncio.run(notebook_index.main())
if __name__ == '__main__':
    main()
    

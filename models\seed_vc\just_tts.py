import asyncio
import os
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
import notebook_index
import models.seed_vc.index as index

model_dir = r"E:\workspace\python\voice-clone\seed-vc"
sys.path.append(model_dir)
def prepare():
    print('prepare')


def clone_voice(origin_file, target_file, tts_text, tts_file, emotion = 'neutral'):
    print('clone_voice')
    return True
tasks = []
async def tts_voice(tts_text, tts_file, origin_file, emotion):
    # 设置最大并发数为
    if len(tasks) >= 16:
        await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
    tasks.append(asyncio.create_task(index.tts_voice(tts_text, tts_file, origin_file, emotion)))
def main():
    notebook_index.prepare = prepare
    notebook_index.clone_voice = clone_voice
    notebook_index.tts_voice = tts_voice
    asyncio.run(notebook_index.main())
    # 等待 tasks 中的所有任务完成
    asyncio.run(asyncio.wait(tasks))
if __name__ == '__main__':
    main()
    




import os
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from tools.ignore_by_ai.train import filter_empty_rows
from tools.ignore_by_ai.infer import predict_batch, prepare
from config import config
from notebook_index import read_csv, replace_parentheses

ignore_set = None
must_real_list = ["我", "你", "他", "它", "她", "停", "走", "爬", "好", "不", "人",
                  "在", "有", "这", "个", "上", "下", "到", "说", "着", "就", "和", "出", "后", "自", "但"]


# 通过ai忽略不需要翻译音频的英文

def is_real_row(row,df,index):
    en = row[config.en_column]
    zh = replace_parentheses(row[config.zh_column])
    df.at[index, config.zh_column] = zh
    file_path = row[config.file_path_column]
    if not filter_empty_rows(row):
        return False
    # 过滤龙吼
    if file_path.find('voicepower') !=-1:
        return False
    global ignore_set
    if ignore_set is None:
        ignore_set = set(read_csv(os.path.join(
            os.path.dirname(__file__), './ignore.csv'))['ignore'])
    # 如果zh中包含must_real里的值
    if any(must_real in zh for must_real in must_real_list):
        return True
    # 常用过滤集合
    if zh in ignore_set or en in ignore_set:
        return False
    # AI预测过滤
    if predict_batch([zh])[0] == 1:
        return False
    return True
def ignore():
    df = read_csv(config.config_file)
    prepare()
    for index, row in df.iterrows():
        origin_ignore = row[config.ignore_column] if config.ignore_column in df.columns else None
        if not is_real_row(row,df,index):
            df.at[index, config.ignore_column] = 1
        else:
            df.at[index, config.ignore_column] = 0
        if origin_ignore is not None and str(df.at[index, config.ignore_column]) != str(origin_ignore):
            print(f'{row[config.zh_column]},原来{origin_ignore},现在{df.at[index, config.ignore_column]}')
    # 获取config.file的文件名
    filename = f"{os.path.basename(config.config_file).split('.')[-2]}-resolved.csv"
    output = os.path.join(os.path.dirname(config.config_file), filename)
    print(output)
    # 另存
    df.to_csv(output, index=False, encoding='utf-8_sig')
    return output


if __name__ == '__main__':
    ignore()

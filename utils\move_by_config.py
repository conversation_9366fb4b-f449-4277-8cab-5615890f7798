import shutil
import os
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from notebook_index import read_csv, filter_row, replace_suffix, resolve_df
from config import config


# 根据配置文件复制生成的wav和lip文件到不同后缀的文件夹中，用于构建最后替换原音频文件的文件夹
def move():
    df = read_csv(config.config_file)
    df = resolve_df(df)
    res = set()
    for index, row in df.iterrows():
        if not filter_row(row):
            # print("忽略")
            continue
        wav_path = row[config.file_path_column]
        target_file = os.path.join(config.target_folder, wav_path)
        if os.path.exists(target_file):
            suffix = os.path.splitext(row[config.origin_file_path_column])[1]
            res.add(suffix)
            # 移动新生成的wav
            dst_file = os.path.join(config.target_folder, suffix, wav_path)
            dst_folder = os.path.dirname(dst_file)
            os.makedirs(dst_folder, exist_ok=True)
            shutil.copy2(target_file, dst_file)
            # 移动原lip
            lip_path = replace_suffix(
                row[config.origin_file_path_column], ".lip")
            lip_file = os.path.join(config.wav_file_dir, lip_path)
            dst_file = os.path.join(config.target_folder, suffix, lip_path)
            if os.path.exists(lip_file):
                shutil.copy2(lip_file, dst_file)
            else:
                print("不存在lip_file%s" % (lip_file))
            # 移动新生成的lip
            new_lip_file = os.path.join(
                config.target_folder, config.gen_fuz_path, lip_path)
            if os.path.exists(new_lip_file):
                shutil.copy2(new_lip_file, dst_file)
        else:
            print("不存在%s" % (target_file))
    return ','.join(res)


if __name__ == '__main__':
    move()

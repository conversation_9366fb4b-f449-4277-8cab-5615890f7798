


import json
import os
import sys


sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from config import config
from notebook_index import read_csv

def read_json(path):
    if not os.path.exists(path):
        return {}
    with open(path, 'r', encoding="utf-8") as f:
        return json.loads(f.read())
def ignore():
    config.config_file = r"E:\workspace\python\voice-clone\clone-index\temp_config\LazyVoiceFinder_export_oblivion.csv"
    df = read_csv(config.config_file)
    zh_dict = read_json(r"E:\software\mod-tools\FModel\Output\Exports\OblivionRemastered\Content\Localization\Game\zh-Hans\Game.json")
    en_dict = read_json(r"E:\software\mod-tools\FModel\Output\Exports\OblivionRemastered\Content\Localization\Game\en\Game.json")
    # json拍平
    zh_dict = {k: v for item in zh_dict.values() for k, v in item.items()}
    en_dict = {k: v for item in en_dict.values() for k, v in item.items()}
    for index, row in df.iterrows():
        if row[config.zh_column] in zh_dict:
            df.at[index, config.zh_column] = zh_dict[row[config.zh_column]]
        if row[config.en_column] in en_dict:
            df.at[index, config.en_column] = en_dict[row[config.en_column]]
    # 获取config.file的文件名
    filename = f"{os.path.basename(config.config_file).split('.')[-2]}-zh.csv"
    output = os.path.join(os.path.dirname(config.config_file), filename)
    print(output)
    # 另存
    df.to_csv(output, index=False, encoding='utf-8_sig')
    return output


if __name__ == '__main__':
    ignore()

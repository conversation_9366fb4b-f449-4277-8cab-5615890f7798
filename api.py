import os
from fastapi import FastAPI
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

app = FastAPI()


class LoginData(BaseModel):
    filename: str


@app.get("/file")
async def api(filename: str):
    # 如果文件存在，返回文件
    if not os.path.exists(filename):
        return "文件不存在"

    def iterfile():  # (1)
        with open(filename, mode="rb") as file_like:  # (2)
            yield from file_like  # (3)
    return StreamingResponse(iterfile(), media_type="video/wav")


def main():
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8080)


if __name__ == "__main__":
    main()

import asyncio
import json
import math
import os
import shutil
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
# from tools.lip_generator.sample import calculate_scale_factor, get_audio_length
import notebook_index
from utils.tts import tts
from utils.file import is_file_exist, mk_dirs, remove_file_if_exist
# from utils import azure_tts

model_dir = r"E:\workspace\python\voice-clone\seed-vc"
sys.path.append(model_dir)
import inference
def prepare():
    # os.chdir(model_dir)
    # print(f"当前工作目录已切换至: {os.getcwd()}")
    inference.run()


def clone_voice(origin_file, target_file, tts_text, tts_file, emotion = 'neutral'):
    mk_dirs(target_file)
    inference.voice_conversion(tts_file, origin_file, target_file)
    return True

tts_cache_path = os.path.join(os.path.dirname(__file__), "tts-cache.json")
def read_json():
    if not os.path.exists(tts_cache_path):
        return {}
    # 读取 JSON 文件
    with open(tts_cache_path, 'r', encoding='utf-8') as f:
        data = f.read()
    # 将 JSON 字符串转换为 Python 对象
    json_data = json.loads(data)
    return json_data
def write_json(data):
    # 将 Python 对象转换为 JSON 字符串
    json_data = json.dumps(data, ensure_ascii=False, indent=4)
    # 写入 JSON 文件
    with open(tts_cache_path, 'w', encoding='utf-8') as f:
        f.write(json_data)
async def tts_voice(tts_text, tts_file, origin_file, emotion):
    mk_dirs(tts_file)
    retry = 0
    scale = 1
    rate = 1
    cache = read_json()
    while (not is_file_exist(tts_file)) and retry < 3:
        print("tts 重试")
        try:
            if tts_text in cache and is_file_exist(cache[tts_text]):
                print('使用缓存的语音')
                shutil.copyfile(cache[tts_text], tts_file)
            else:
                # 使用 asyncio.wait_for 设置超时时间为10秒
                await asyncio.wait_for(tts(tts_text, tts_file, voice="zh-CN-YunxiNeural", rate="-5%"), timeout=10)
                # scale = calculate_scale_factor(get_audio_length(origin_file), get_audio_length(tts_file))
                # rate = math.floor(abs(scale-1) * 100)
                # rate_str = ('+' if scale-1>0 else '-') + str(rate)+'%'
                # if scale != 1:
                #     print('rate:', rate_str)
                #     # 使用 asyncio.wait_for 设置超时时间为10秒
                #     await asyncio.wait_for(tts(tts_text, tts_file, voice="zh-CN-YunxiNeural", rate=rate_str), timeout=10)
            if is_file_exist(tts_file):
                cache[tts_text] = tts_file
                write_json(cache)
                print('tts完成')
        except asyncio.TimeoutError:
            remove_file_if_exist(tts_file, check_size=False)
            print('tts 超时')
        except Exception as e:
            remove_file_if_exist(tts_file, check_size=False)
            print(f'tts 出现错误: {e}')
        retry += 1
    return is_file_exist(tts_file)
def main():
    notebook_index.prepare = prepare
    notebook_index.clone_voice = clone_voice
    notebook_index.tts_voice = tts_voice
    asyncio.run(notebook_index.main())
if __name__ == '__main__':
    main()
    

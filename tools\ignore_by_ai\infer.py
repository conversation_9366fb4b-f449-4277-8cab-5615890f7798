import os
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from transformers import BertTokenizer, BertForSequenceClassification
import torch
from datasets import load_dataset
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from config import config
from tools.ignore_by_ai.train import filter_empty_rows

tokenizer = None
model= None
device = None
# 定义预处理函数
def preprocess_texts(texts):
    return tokenizer(texts, truncation=True, padding='max_length', max_length=128, return_tensors='pt').to(device)

def predict_batch(texts):
    # 预处理输入文本
    inputs = preprocess_texts(texts)

    # 进行推理
    with torch.no_grad():
        outputs = model(**inputs)

    # 获取预测结果
    logits = outputs.logits
    predictions = torch.argmax(logits, dim=-1)

    # 将预测结果转换为标签
    predicted_labels = predictions.tolist()
    return predicted_labels

def prepare():
    global model, tokenizer, device
    # 加载训练好的模型和分词器
    model_path = os.path.join(os.path.dirname(__file__), 'results')  # 模型保存路径
    tokenizer = BertTokenizer.from_pretrained(model_path)
    model = BertForSequenceClassification.from_pretrained(model_path)

    # 设置模型为评估模式
    model.eval()

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    model.to(device)
if __name__ == "__main__":
    prepare()
    # 示例输入文本
    texts = [
        "你好,我的朋友,天天",
        "汪汪",
        "再见",
        "你好",
        "不！",
        "不。",
        "对。",
        "我。",
        "龙！",
        "看！",
        "不……",
        "当然！",
        "懦夫。",
        "带路吧。",
        "哈，懦夫。",
        "你很勇敢。巴兰 霍克荣。你的战败让我感到光荣。",
        "和平！",
        "停！",
        "都瓦克因！不！！",
    ]
    dataset = load_dataset('csv', data_files='../../temp_config/LazyVoiceFinder_export-ignore.csv').filter(filter_empty_rows)
    texts = dataset['train'][config.zh_column]
    true_labels = dataset['train'][config.ignore_column]
    pred_labels = []
    # 分批次推理
    batch_size = 1024
    for i in range(0, len(texts), batch_size):
        print(f"Processing batch {i} to {i + batch_size - 1}")
        batch_texts = texts[i:i + batch_size]
        batch_true_labels = true_labels[:i + batch_size]
        pred_labels+= predict_batch(batch_texts)
        accuracy = accuracy_score(batch_true_labels, pred_labels)
        precision = precision_score(batch_true_labels, pred_labels)
        recall = recall_score(batch_true_labels, pred_labels)
        f1 = f1_score(batch_true_labels, pred_labels)
        print(f"Accuracy: {accuracy}")
        print(f"Precision: {precision}")
        print(f"Recall: {recall}")
        print(f"F1 Score: {f1}")
    # for text, label in zip(texts, pred_labels):
    #     print(f"Text: {text}, Predicted Label: {label}")

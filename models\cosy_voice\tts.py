import os
import torchaudio
import sys

model_dir = r"E:\workspace\python\voice-clone\CosyVoice"
sys.path.append(model_dir)

from cosyvoice.utils.file_utils import load_wav
from cosyvoice.cli.cosyvoice import CosyVoice2
sys.path.append(os.path.join(model_dir, 'third_party/Matcha-TTS'))
cosyvoice = CosyVoice2(os.path.join(model_dir, 'pretrained_models/CosyVoice2-0.5B'),
                       load_jit=False, load_trt=False, fp16=False)


# for i, j in enumerate(cosyvoice.inference_zero_shot('收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。', '希望你以后能够做的比我还好呦。', prompt_speech_16k, stream=False)):
#     torchaudio.save('zero_shot_{}.wav'.format(i), j['tts_speech'], cosyvoice.sample_rate)
def get_instruction(emotion):
    emotion_dict = {
        # 'Neutral': '用普通话说',
        'Anger': '用生气的语气说',
        'Happy': '用开心的语气说',
        'Fear': '用恐惧的情感表达',
        'Sad': '用伤心的语气说',
        'Disgust': '用恶心的情感表达',
        'Surprise': '用惊讶的语气说',
        'Puzzled': '用疑惑的语气说',
    }
    if (emotion in emotion_dict):
        return emotion_dict[emotion]
    return None


def tts(text, output, prompt_speech_path, emotion):
    prompt_speech_16k = load_wav(prompt_speech_path, 16000)
    # for i, j in enumerate(cosyvoice.inference_zero_shot(text, "", prompt_speech_16k, stream=False)):
    #     torchaudio.save(output, j['tts_speech'], cosyvoice.sample_rate)
    print('语气:%s'%emotion)
    # text_frontend为False，不切分，否则需要合并，合并的语速会变快
    emotion_text = get_instruction(emotion)
    if (emotion_text is not None):
        text = f"{get_instruction(emotion)}<|endofprompt|>{text}"
    for i, j in enumerate(cosyvoice.inference_cross_lingual(text, prompt_speech_16k, stream=False, speed=1.0, text_frontend=False)):
    # for i, j in enumerate(cosyvoice.inference_instruct2(text, get_instruction(emotion), prompt_speech_16k, stream=False, text_frontend=False)):
        torchaudio.save(output, j['tts_speech'], cosyvoice.sample_rate)
    return True
if __name__ == '__main__':
    tts("你发动了这场内战，使天际陷入叛乱，而现在帝国将会把你们剿灭，恢复天际的和平。","output.wav",r"mq101__001027e5_1.wav","Disgust")
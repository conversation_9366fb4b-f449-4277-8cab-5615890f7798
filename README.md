准备数据
1、准备配置文件；通过[Lazy Voice Finder](https://www.nexusmods.com/skyrim/mods/82482)加载.esm，找到字幕和音频的对应关系，导出为csv
2、准备音频；通过[BAE](https://www.nexusmods.com/skyrimspecialedition/mods/974)解包bsa文件拿到音频目录sound，通过[YakitoriAudioConverter](https://www.nexusmods.com/skyrimspecialedition/mods/17765)解压.fuz格式文件，生成.wav音频文件和.lip口型文件。
3、准备AI模型，下面列入常见的一些模型，可任选
[seed_vc](https://github.com/Plachtaa/seed-vc)：生成速度快，较稳定，但没有tts，需要安装edge-tts python包先生成语音再克隆音色。
[cosyvoice](https://github.com/FunAudioLLM/CosyVoice)：克隆速度慢，但生成语气效果好，可进行情绪控制，但不够稳定，有时语气有点怪异。
[spark-tts](https://github.com/SparkAudio/Spark-TTS)：克隆速度较慢，生成语气效果最好，但最不稳定，容易生成错误的音频。

克隆步骤
1、预处理配置文件，忽略拟声词，歌声等无需翻译的音频
2、遍历配置文件，按中文字幕+原始英文配音文件，生成中文配音文件。
3、根据中文的配音文件+中文字幕，生成口型文件。
4、将生成的中文配音文件和口型文件放到同一目录，通过[YakitoriAudioConverter](https://www.nexusmods.com/skyrimspecialedition/mods/17765)合并回原始格式（如.fuz）。

## 注意
需要替换模型的引用地址
如`seed_vc\seed_vc_index.py`文件中的`modelDir`

导出的配置文件需要如下列
Dialogue 1 - English
Dialogue 2 - Chinese
Path
Voice Type
Emotion

如果要生成口型文件，还要如下列：
State
Plugin
File Name


## 滚4重制版
滚4原版音频是.mp3格式，需要设置config.wav_suffix='.mp3'，翻译文本需要通过fmodel导出json，通过LOC代码映射到中文生成csv。

滚4重制版的音频是.wem格式，音色克隆后，需要转换为.wem格式，且文件名要对应上，需要通过fmodel导出映射关系，wwise转换为.wem格式。

最后通过UnrealPak打包。

import os
import azure.cognitiveservices.speech as speechsdk
region = 'eastasia'
key = '4RasRQhSOhrfh5Kg7ECiftTOqIwsauf1YZeeIyTrIHI0OwMgQCEIJQQJ99BCAC3pKaRXJ3w3AAAYACOGdO8e'


# 获取情感指令
def get_instruction(emotion):
    emotion_dict = {
        'Neutral': '',
        'Anger': 'angry',
        'Happy': 'cheerful',
        'Fear': 'fearful',
        'Sad': 'sad',
        'Disgust': 'disgruntled',
        'Surprise': '',
        'Puzzled': 'embarrassed',
    }
    if (emotion in emotion_dict):
        return emotion_dict[emotion]
    return ''


def mkssml(voice, text, style, rate="+0%", styledegree=1) -> str:
    return (
        "<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='en-US'>"
        f"<voice name='{voice}' rate='{rate}' style='{style}' styledegree='{styledegree}'>"
        f"{text}"
        "</voice>"
        "</speak>"
    )


def tts(text, output="output.wav", voice='zh-CN-YunxiNeural', emotion="", rate="+0%", styledegree=1):
    speech_config = speechsdk.SpeechConfig(subscription=key, region=region)
    speech_config.speech_synthesis_voice_name = voice
    speech_synthesizer = speechsdk.SpeechSynthesizer(
        speech_config=speech_config, audio_config=None)

    ssml_string = mkssml(voice=voice, text=text,
                         style=get_instruction(emotion), rate=rate, styledegree=styledegree)
    speech_synthesis_result = speech_synthesizer.speak_ssml_async(
        ssml_string).get()

    stream = speechsdk.AudioDataStream(speech_synthesis_result)
    stream.save_to_wav_file(output)


if __name__ == '__main__':
    tts("军团一直在寻找强壮能干的武士。如果你符合条件，可以去我们在独孤城的总部应征", output="output.wav",
        emotion="Neutral", rate="-10%", styledegree=1.5)

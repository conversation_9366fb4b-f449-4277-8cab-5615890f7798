# 配置日志记录
import logging
import logging.handlers
import os


logger = logging.getLogger('logger')
logger.setLevel(logging.INFO)  # 设置日志记录级别为 INFO
# 创建文件处理器并设置编码为 UTF-8
log_file = os.path.join(os.getcwd(), 'temp_config/logger.log')
file_handler = logging.handlers.RotatingFileHandler(log_file, encoding='utf-8')
file_handler.setLevel(logging.INFO)

# 创建格式化器
formatter = logging.Formatter(
    '%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
file_handler.setFormatter(formatter)

# 将文件处理器添加到日志记录器
logger.addHandler(file_handler)
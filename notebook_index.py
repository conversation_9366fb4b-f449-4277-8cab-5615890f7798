# notebook_index.py
import json
import asyncio
import os
import re
import pandas as pd
from config import config
from utils.file import add_file_to_zip, is_file_exist, mk_dirs, path_to_lower_in_tree, remove_file_if_exist  # 导入Config类

def filter_row(row):
    en = row[config.en_column]
    zh = row[config.zh_column]
    file_path = row[config.file_path_column]
    # 为空
    if pd.isna(en) or str(en).strip() == '' or pd.isna(zh) or str(zh).strip() == '' or pd.isna(file_path) or str(file_path).strip() == '':
        return False
    # 配置过滤
    if str(row['ignore']) == '1':
        return False
    return True


def read_csv(file: str):
    df = pd.read_csv(
        file, encoding='utf-8', dtype=str)
    # 将NaN替换为空字符串
    df.fillna('', inplace=True)
    return df


def replace_suffix(file_path: str, suffix='.wav'):
    # 定义需要替换的后缀列表
    suffixes = ['.fuz', '.xwm', '.lip', '.wav', '.mp3']
    # 使用正则表达式替换所有匹配的后缀
    pattern = re.compile(r'(' + '|'.join(re.escape(s)
                         for s in suffixes) + r')$')
    return pattern.sub(suffix, file_path)

# 替换括号内容


def replace_parentheses(text):
    if text is None: return ''
    new_text = re.sub(r'[\(（][^\)）]*[\)）]', '', text).strip()
    new_text = re.sub(r'\*(叹息|叹气)\*', '唉~', new_text).strip()
    new_text = re.sub(r'\*(大笑|笑)\*', '哈哈，', new_text).strip()
    new_text = re.sub(r'\*(哭泣)\*', '呜呜呜~', new_text).strip()
    new_text = re.sub(r'\*(打嗝|嗝)\*', '嗝~', new_text).strip()
    new_text = re.sub(r'\*(傻笑)\*', '呵呵~', new_text).strip()
    new_text = re.sub(r'\*[^\*]*\*', '', new_text).strip()
    return new_text


def resolve_df(df: pd.DataFrame):
    path_dict = {}
    if config.path_to_lower:
        path_dict = path_to_lower_in_tree(config.wav_file_dir, config.wav_suffix)
    # 更新df file_path_column的数据
    for index, row in df.iterrows():
        path_str = str(row[config.file_path_column])
        wav_file = replace_suffix(path_str, config.wav_suffix)
        if config.path_to_lower and wav_file in path_dict:
            path_str = path_dict[wav_file]
        df.at[index, config.file_path_column] = wav_file
        df.at[index, config.origin_file_path_column] = path_str
        df.at[index, config.zh_column] = replace_parentheses(
            str(row[config.zh_column])).replace(" ", "")
    return df


def read_log():
    if not os.path.exists(config.log_json):
        return {}
    with open(config.log_json, 'r', encoding="utf-8") as f:
        return json.loads(f.read())


def write_log(resolved_dict):
    json_str = json.dumps(resolved_dict, indent=4, ensure_ascii=False)
    mk_dirs(config.log_json)
    with open(config.log_json, 'w', encoding="utf-8") as json_file:
        json_file.write(json_str)


def set_status(resolved_dict, group_name, row, status):
    if row[config.file_path_column] in resolved_dict[group_name]['ignore']:
        del resolved_dict[group_name]['ignore'][row[config.file_path_column]]
    if row[config.file_path_column] in resolved_dict[group_name]['noexist']:
        del resolved_dict[group_name]['noexist'][row[config.file_path_column]]
    if row[config.file_path_column] in resolved_dict[group_name]['fail']:
        del resolved_dict[group_name]['fail'][row[config.file_path_column]]
    if row[config.file_path_column] in resolved_dict[group_name]['complete']:
        del resolved_dict[group_name]['complete'][row[config.file_path_column]]
    resolved_dict[group_name][status][row[config.file_path_column]] = {}


async def prepare():
    raise Exception("prepare not implemented")


async def tts_voice(tts_text, tts_file, origin_file, emotion):
    raise Exception("clone_voice not implemented")


def clone_voice(origin_file, target_file, tts_text, tts_file, emotion):
    raise Exception("clone_voice not implemented")


async def main():
    resolved_dict = read_log()
    df = read_csv(config.config_file)
    df = resolve_df(df)
    total = len(df)
    count = 0
    grouped_df = df.groupby(config.group_column)
    prepare()
    # 分组处理
    for group_name, group in grouped_df:
        if group_name not in resolved_dict:
            resolved_dict[group_name] = {
                'ignore': {},
                "complete": {},
                'noexist': {},
                "fail": {}
            }
        resolved_dict[group_name]["noexist"] = {}
        resolved_dict[group_name]["ignore"] = {}
        resolved_dict[group_name]["fail"] = {}
        # 移动分组下所有wav到训练文件夹
        for index, row in group.iterrows():
            try:
                if config.stop:
                    return
                print('进度 %s/%s 路径:%s 中文:%s' % (count, total,
                      row[config.file_path_column], row[config.zh_column]))
                format_path = row[config.file_path_column].replace("\\", "/")
                complete = row[config.file_path_column] in resolved_dict[group_name]['complete'] or format_path in resolved_dict[group_name]['complete']
                row[config.file_path_column] = format_path
                origin_file = os.path.join(
                    config.wav_file_dir, row[config.file_path_column])
                target_file = os.path.join(
                    config.target_folder, row[config.file_path_column])
                # 判断是否忽略
                if not filter_row(row):
                    print("忽略")
                    count += 1
                    set_status(resolved_dict, group_name, row, "ignore")
                    remove_file_if_exist(target_file, check_size=False)
                    continue
                # 判断源文件是否存在
                if not is_file_exist(origin_file):
                    print("不存在")
                    count += 1
                    set_status(resolved_dict, group_name, row, "noexist")
                    continue
                if is_file_exist(target_file) and complete:
                    print("已完成，跳过")
                    count += 1
                    continue
                if config.need_tts:
                    # tts
                    tts_file = os.path.join(
                        config.tts_folder, row[config.file_path_column])
                    if not await tts_voice(tts_text=row[config.zh_column], tts_file=tts_file, origin_file = origin_file, emotion=row[config.emotion_column]):
                        print("tts 失败")
                        count += 1
                        set_status(resolved_dict, group_name, row, "fail")
                        continue
                else:
                    tts_file = None

                print('开始处理')
                if clone_voice(origin_file, target_file, row[config.zh_column], tts_file, row[config.emotion_column]):
                    # 处理该条语音
                    set_status(resolved_dict, group_name, row, "complete")
                    print('完成')
                else:
                    set_status(resolved_dict, group_name, row, "fail")
                    print("clone 失败")
                count += 1

                write_log(resolved_dict)
                # 后处理
                if config.remove_tts_file:
                    remove_file_if_exist(tts_file, check_size=False)
                if config.zip_target:
                    add_file_to_zip(target_file, True)
            except Exception as e:
                print(e)
                set_status(resolved_dict, group_name, row, "fail")
                write_log(resolved_dict)

        write_log(resolved_dict)


if __name__ == '__main__':
    asyncio.run(main())

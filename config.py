# config.py
import os


class Config:
    def __init__(self):
        self.en_column = 'Dialogue 1 - English'
        self.file_path_column = 'Path'
        self.origin_file_path_column = 'Origin Path'
        self.zh_column = 'Dialogue 2 - Chinese'
        self.group_column = 'Voice Type'
        self.emotion_column = 'Emotion'
        self.ignore_column = 'ignore'
        self.wav_file_dir = r"C:\games\JueLun V5\Source\origin"
        self.wav_suffix = '.wav'
        self.cwd = os.getcwd()
        self.dir = os.path.dirname(__file__)
        self.config_file = os.path.join(
            self.dir, "temp_config\LazyVoiceFinder_export-ignore.csv")
        self.target_folder = os.path.join(self.cwd, r"dist\target")
        self.log_json = os.path.join(self.cwd, r'dist\logs.json')
        self.tts_folder = os.path.join(self.cwd, r"dist\tts")
        self.gen_fuz_path = 'gen_lip_res'

        self.zip_target = False
        self.remove_tts_file = False
        self.need_tts = True
        self.path_to_lower = False
        self.gen_fuz = False
        self.only_scale = False

        self.run_lip_path = r"E:\workspace\python\voice-clone\clone-index\tools\addons\Runalip\Runalip.exe"

        self.stop = False

config = Config()

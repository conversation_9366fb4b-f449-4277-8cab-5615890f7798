import edge_tts
import asyncio
import sys


async def tts(text, output, voice='zh-CN-YunxiNeural', rate='+0%', volume='+0%', pitch="+0Hz",):
    tts = edge_tts.Communicate(
        text=text, voice=voice, rate=rate, volume=volume, pitch=pitch)
    try:
        await tts.save(output)
    except Exception:
        print("Error:", sys.exc_info()[0])
        return False
    return True

if __name__ == '__main__':
    TEXT = sys.argv[1] if len(
        sys.argv) > 1 else '军团一直在寻找强壮能干的武士。如果你符合条件，可以去我们在独孤城的总部应征。'
    voice = sys.argv[2] if len(sys.argv) > 2 else 'zh-CN-YunxiNeural'
    rate = sys.argv[3] if len(sys.argv) > 3 else '+0%'
    volume = sys.argv[4] if len(sys.argv) > 4 else '+0%'
    pitch = sys.argv[5] if len(sys.argv) > 5 else "+0Hz"
    output = r'output.wav'
    asyncio.run(tts(text=TEXT, output=output,
                voice=voice, rate=rate, volume=volume, pitch=pitch))

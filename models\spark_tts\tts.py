import os
import torch
import soundfile as sf
import logging
import sys

model_dir = r"E:\workspace\python\voice-clone\Spark-TTS"
sys.path.append(model_dir)
from tts.SparkTTS import SparkTTS
device = torch.device(f"cuda:0")
model = SparkTTS(os.path.join(
    model_dir, "pretrained_models/Spark-TTS-0.5B"), device)


def tts(text, output, prompt_speech_path):
    max_duration = len(text) / 2.5 + 2
    retry = 1
    while True:
        with torch.no_grad():
            wav = model.inference(
                text,
                prompt_speech_path,
                # prompt_text="",
                # gender=args.gender,
                # pitch=args.pitch,
                # speed='high',
            )
        duration = len(wav) / 16000  # 计算wav的时长（秒）
        if retry > 3:
            logging.warning(
                f"Generated audio duration {duration:.2f}s exceeds max duration {max_duration}s. Aborting.")
            return False
        if duration <= max_duration:
            sf.write(output, wav, samplerate=16000)
            break
        else:
            retry += 1
            logging.warning(
                f"Generated audio duration {duration:.2f}s exceeds max duration {max_duration}s. Retrying inference.")
    return True

if __name__ == "__main__":
    tts("你好我的朋友", "output.wav", "example/prompt_audio.wav")
    tts("你好我的朋友，你是猪么？", "output2.wav", "example/prompt_audio.wav")

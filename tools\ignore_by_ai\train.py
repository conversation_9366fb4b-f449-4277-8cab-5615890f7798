import os
import re
import sys

import pandas as pd

os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"

import numpy as np
from transformers import BertTokenizer, BertForSequenceClassification, Trainer, TrainingArguments
from datasets import load_dataset, Value
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from config import config
from notebook_index import replace_parentheses


# 过滤,非空行，非歌曲，非全英文
def filter_empty_rows(row):
    en = row[config.en_column]
    zh = replace_parentheses(row[config.zh_column])
    file_path = row[config.file_path_column]
    # 为空
    if pd.isna(en) or str(en).strip() == '' or pd.isna(zh) or str(zh).strip() == '' or pd.isna(file_path) or str(file_path).strip() == '':
        return False
    # 过滤歌曲
    if file_path.find('bardsong') !=-1:
        return False
    # 全英文
    if not bool(re.search(r'[\u4e00-\u9fff]', re.sub(r'[！。？，“”‘’；——《》]', '', zh))):
        return False
    return True




def train():
    # 加载数据集
    dataset = load_dataset('csv', data_files='../../temp_config/LazyVoiceFinder_export-ignore.csv')
    dataset = dataset.filter(filter_empty_rows)

    # 确保标签列正确传递
    dataset = dataset.rename_column(config.ignore_column, 'label')
    dataset = dataset.cast_column('label', Value('int32'))

    print("features", dataset['train'].features)

    # 分割数据集为训练集和验证集
    dataset = dataset['train'].train_test_split(test_size=0.1)

    # 打印数据集分割结果
    print(f"Train dataset size: {len(dataset['train'])}")
    print(f"Test dataset size: {len(dataset['test'])}")

    # 加载支持中文的BERT分词器和模型
    tokenizer = BertTokenizer.from_pretrained('bert-base-chinese')
    model = BertForSequenceClassification.from_pretrained('bert-base-chinese', num_labels=2)
    # 数据预处理
    def preprocess_function(examples):
        return tokenizer(examples[config.zh_column], truncation=True, padding='max_length', max_length=128)

    encoded_dataset = dataset.map(preprocess_function, batched=True)

    # 打印预处理后的数据集特征
    print("Encoded dataset features:", encoded_dataset['train'].features)

    # 设置训练参数
    training_args = TrainingArguments(
        output_dir='./results',
        evaluation_strategy='epoch',
        learning_rate=2e-5,
        per_device_train_batch_size=8,
        per_device_eval_batch_size=8,
        num_train_epochs=3,
        weight_decay=0.01,
        save_strategy='epoch',  # 确保模型和分词器在每个 epoch 结束时保存
        save_total_limit=2,      # 保留最近的 2 个保存点
    )

    # 定义计算评估指标的函数
    def compute_metrics(eval_pred):
        logits, labels = eval_pred
        predictions = np.argmax(logits, axis=-1)
        accuracy = accuracy_score(labels, predictions)
        precision = precision_score(labels, predictions)
        recall = recall_score(labels, predictions)
        f1 = f1_score(labels, predictions)
        return {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1
        }

    # 定义Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=encoded_dataset['train'],
        eval_dataset=encoded_dataset['test'],
        compute_metrics=compute_metrics,
    )

    # 开始训练
    trainer.train()

    # 评估模型
    eval_results = trainer.evaluate()
    print(f"Eval results: {eval_results}")

    # 保存模型和分词器
    model.save_pretrained('./results')
    tokenizer.save_pretrained('./results')

    # 生成预测结果
    predictions = trainer.predict(encoded_dataset['test'])
    pred_labels = predictions.predictions.argmax(-1)

    # 获取真实标签
    true_labels = encoded_dataset['test']['label']
    # 计算评估指标
    accuracy = accuracy_score(true_labels, pred_labels)
    precision = precision_score(true_labels, pred_labels)
    recall = recall_score(true_labels, pred_labels)
    f1 = f1_score(true_labels, pred_labels)

    print(f"Accuracy: {accuracy}")
    print(f"Precision: {precision}")
    print(f"Recall: {recall}")
    print(f"F1 Score: {f1}")

if __name__ == "__main__":
    train()
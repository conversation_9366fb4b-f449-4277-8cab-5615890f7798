import json
import os
import shutil
import sys



sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from config import config
from notebook_index import filter_row, read_csv, replace_suffix, resolve_df
from pydub import AudioSegment
from utils.file import is_file_exist, mk_dirs
from utils.utlis import run_command


def walk_files(src_dir):
    result = {}
    output = os.path.join(os.path.dirname(__file__), 'result.json')
    for root, dirs, files in os.walk(src_dir):
        for file in files:
            src_file = os.path.join(root, file)
            try:
                # 加载json
                if not os.path.exists(src_file):
                    return {}
                with open(src_file, 'r', encoding="utf-8") as f:
                    config_list = json.loads(f.read())
                    for config in config_list:
                        if 'EventCookedData' not in config:
                            continue
                        if 'EventLanguageMap' not in config['EventCookedData']:
                            continue
                        value_list = config['EventCookedData']['EventLanguageMap']
                        name = (config['EventCookedData']['DebugName'].replace(
                            "Play_", "") + ".wav").replace("_sid.wav", ".wav")
                        #  遍历value_list
                        for value in value_list:
                            media_list = value["Value"]['Media']
                            for media in media_list:
                                media_id = media['MediaId']
                                result[name] = {
                                    "id": media_id,
                                    "config": os.path.relpath(src_file, src_dir),
                                }
            except Exception as e:
                print(src_file)
                print(e)
        # 生成json文件
        with open(output, 'w', encoding="utf-8") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)
    return result


def to_wem_wav():
    target_folder = r"E:\workspace\python\voice-clone\clone-index\dist\target\seed_vc_oblivion2"
    wem_folder = os.path.join(target_folder, "wem")
    df = read_csv(
        r"E:\workspace\python\voice-clone\clone-index\temp_config\LazyVoiceFinder_export_oblivion-zh-resolved.csv")
    df = resolve_df(df)
    config_path = os.path.join(os.path.dirname(__file__), 'result.json')
    with open(config_path, 'r', encoding="utf-8") as f:
        id_config = json.loads(f.read())
    for index, row in df.iterrows():
        if not filter_row(row) and row[config.file_path_column].find("altvoice") == -1 and row[config.file_path_column].find("beggar") == -1:
            continue
        wav_path = row[config.file_path_column]
        # 根据路径构造key
        relative_path = os.path.relpath(wav_path, "\\".join(wav_path.split('\\')[:3]))
        key = replace_suffix(relative_path.replace(
            "//", "_").replace("\\", "_").replace(" ", "_"), '.wav')

        # 克隆的目标文件
        target_file = os.path.join(
            target_folder, wav_path).replace("\\altvoice\\", "\\").replace("\\beggar\\", "\\")

        if os.path.exists(target_file):
            if key in id_config:
                id_name = id_config[key]["id"]
                # 输出的文件
                dst_file = os.path.join(wem_folder, f"{id_name}.wav")
                mk_dirs(dst_file)
                if is_file_exist(dst_file):
                    continue
                # 如果是mp3格式，转换为wav格式
                if not target_file.endswith(".wav"):
                    # mp3格式转为wav
                    mp3_audio = AudioSegment.from_mp3(
                        target_file)
                    mp3_audio.export(dst_file, format="wav")
                else:
                    shutil.copy2(target_file, dst_file)
            else:
                print("不存在%s" % (key))


def trans_files(src_dir, target_dir, wem_folder):
    result = {}
    output = os.path.join(os.path.dirname(__file__), 'result.json')
    for root, dirs, files in os.walk(src_dir):
        for file in files:
            src_file = os.path.join(root, file)
            try:
                # 加载json
                if not os.path.exists(src_file):
                    return {}
                with open(src_file, 'r', encoding="utf-8") as f:
                    config_list = json.loads(f.read())
                    for config in config_list:
                        if 'EventCookedData' not in config:
                            continue
                        if 'EventLanguageMap' not in config['EventCookedData']:
                            continue
                        value_list = config['EventCookedData']['EventLanguageMap']
                        name = config['EventCookedData']['DebugName'].replace(
                            "Play_", "") + ".wav"
                        #  遍历value_list
                        for value in value_list:
                            media_list = value["Value"]['Media']
                            for media in media_list:
                                media_id = media['MediaId']
                                result[name] = {
                                    "id": media_id,
                                    "config": os.path.relpath(src_file, src_dir),
                                }
                                paths = os.path.dirname(os.path.relpath(
                                    src_file, src_dir)).split("\\")[1:]
                                file_name = name.replace(
                                    "_".join(paths)+"_", "")

                                target_file = replace_suffix(os.path.join(target_dir, "\\".join(
                                    paths), file_name), ".mp3").replace("\\altvoice\\", "\\").replace("\\beggar\\", "\\")

                                if is_file_exist(target_file):
                                    wem_file = os.path.join(
                                        wem_folder, f"{media_id}.wav")
                                    if is_file_exist(wem_file):
                                        continue
                                    mp3_audio = AudioSegment.from_mp3(
                                        target_file)
                                    mp3_audio.export(wem_file, format="wav")
                                else:
                                    print("不存在%s" % (target_file))

            except Exception as e:
                print(src_file)
                print(e)
        # 生成json文件
        with open(output, 'w', encoding="utf-8") as f:
            json.dump(result, f, indent=4, ensure_ascii=False)
    return result


def rename_wem():
    src_dir = r"E:\software\Wwise2023.1.8.8601\project\ob2\.cache\Windows\Voices\English(US)\wem"
    dst_dir = r"E:\software\mod-tools\UnrealPak\Chinese_Voice_P\OblivionRemastered\Content\WwiseAudio\Media\English(US)"
    for root, dirs, files in os.walk(src_dir):
        for file in files:
            src_file = os.path.join(root, file)
            # 重命名
            new_name = f"{file.split('_')[0]}.wem"
            new_file = os.path.join(dst_dir, new_name)
            shutil.copy2(src_file, new_file)

def to_wem():
    target_folder = r"E:\workspace\python\voice-clone\clone-index\dist\target\seed_vc_oblivion2"
    wem_folder = os.path.join(target_folder, "wem")
    tasks = []
    count = 0
    # 遍历wem文件夹
    for root, dirs, files in os.walk(wem_folder):
        for file in files:
            src_file = os.path.join(root, file)
            tasks.append(src_file)
            if len(tasks) > 10:
                # 执行命令
                run_command(f'E:\software\mod-tools\sound2wem\zSound2wem.cmd {" ".join(tasks)}')
                count+= len(tasks)
                print(f"转换完成 {count}个文件")
                tasks = []
if __name__ == "__main__":
    # trans_files(src_dir=r"E:\software\mod-tools\FModel\Output\Exports\OblivionRemastered\Content\WwiseAudio\Events\Voice",
    #            target_dir=r"E:\workspace\python\voice-clone\clone-index\dist\target\seed_vc_oblivion2\sound\voice\altarespmain.esp",
    #            wem_folder=r"E:\workspace\python\voice-clone\clone-index\dist\target\seed_vc_oblivion2\wem")
    # walk_files(r"E:\software\mod-tools\FModel\Output\Exports\OblivionRemastered\Content\WwiseAudio\Events\Voice")
    # to_wem_wav()
    rename_wem()
    # to_wem()

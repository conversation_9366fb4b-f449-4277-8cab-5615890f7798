# 生成一个大写文件夹到小写文件夹的映射字典
import os
import shutil
import uuid
import zipfile

# 遍历目录树，返回一个字典，键为小写路径，值为真实路径


def path_to_lower_in_tree(directory, suffix=".wav", path_dict={}):
    for root, dirs, files in os.walk(directory):
        for name in files:
            if name.endswith(suffix):
                real_path = os.path.join(root, name)
                relative_path = os.path.relpath(real_path, directory)
                lower_path = relative_path.lower()
                path_dict[lower_path] = real_path
    return path_dict


# 创建文件所在的文件夹
def mk_dirs(file):
    if not os.path.exists(os.path.dirname(file)):
        os.makedirs(os.path.dirname(file))

# 判断文件是否存在


def is_file_exist(file_path, check_size=True):
    return os.path.exists(file_path) and os.path.isfile(file_path) and (not check_size or os.stat(file_path).st_size > 0)

# 删除文件


def remove_file_if_exist(file_path, check_size=False):
    if is_file_exist(file_path=file_path, check_size=check_size):
        os.remove(file_path)


# 生成一个随机的压缩包文件名
def get_random_zip_filename(base_name='target', suffix='.zip'):
    return f"{base_name}_{uuid.uuid4()}{suffix}"


# 将文件添加到压缩包中
def add_file_to_zip(file_to_add, delete=False, max_size_mb=1000, base_name='target'):
    if not os.path.exists(file_to_add) or not os.path.isfile(file_to_add):
        return
    max_size_bytes = max_size_mb * 1024 * 1024  # 将MB转换为字节

    # 获取当前目录下的所有压缩包文件
    zip_files = [f for f in os.listdir('.') if f.endswith(
        '.zip') and f.find(base_name) != -1]

    # 初始化当前压缩包文件名
    current_zip_filename = None

    # 找到当前最大的压缩包文件
    for zip_file in zip_files:
        with zipfile.ZipFile(zip_file, 'r') as zipf:
            current_size = sum(info.file_size for info in zipf.infolist())
            if current_size < max_size_bytes:
                current_zip_filename = zip_file
                break

    # 如果没有找到合适的压缩包，则创建一个新的随机命名的压缩包
    if current_zip_filename is None:
        current_zip_filename = get_random_zip_filename()

    # 将文件添加到压缩包中
    with zipfile.ZipFile(current_zip_filename, 'a') as zipf:
        zipf.write(file_to_add)
        print(f"文件 {file_to_add} 已成功添加到 {current_zip_filename}")
    if delete and os.path.exists(file_to_add) and os.path.isfile(file_to_add):
        os.remove(file_to_add)


def copy_files(src_dir, dst_dir, suffix='.wav'):
    for root, dirs, files in os.walk(src_dir):
        for file in files:
            if file.endswith(suffix):
                # 获取文件的相对路径
                relative_path = os.path.relpath(root, src_dir)
                # 构建目标文件夹的完整路径
                dst_folder = os.path.join(dst_dir, relative_path)
                # 确保目标文件夹存在
                os.makedirs(dst_folder, exist_ok=True)
                # 构建源文件和目标文件的完整路径
                src_file = os.path.join(root, file)
                dst_file = os.path.join(dst_folder, file)
                # 复制文件
                shutil.copy2(src_file, dst_file)


if __name__ == '__main__':
    # 使用示例
    source_directory = r'E:\workspace\python\voice-clone\seed-vc-main\dist\newTarget\fuz'
    destination_directory = r'E:\workspace\python\voice-clone\seed-vc-main\dist\newTarget\fuz'
    copy_files(source_directory, destination_directory, '.fuz')

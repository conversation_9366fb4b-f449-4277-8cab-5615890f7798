import os
import subprocess
from pypinyin import Style, pinyin
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from utils.logger import logger

# 执行命令
def run_command(command):
    logger.info(f'command {command}')
    startupinfo = subprocess.STARTUPINFO()
    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW

    sp = subprocess.Popen(command, startupinfo=startupinfo,
                          stdout=subprocess.PIPE, stderr=subprocess.PIPE)

    stdout, stderr = sp.communicate()
    stderr = stderr.decode("utf-8")
    if len(stderr):
        logger.error(f"error: {stderr}")

# 中文转拼音
def chinese_to_pinyin(text):
    # 使用pinyin函数将中文转换为拼音
    # Style.NORMAL 表示普通拼音
    pinyin_list = pinyin(text, style=Style.NORMAL)

    # 将二维列表转换为一维列表，并用空格连接
    pinyin_string = ' '.join([item[0] for item in pinyin_list])

    return pinyin_string

